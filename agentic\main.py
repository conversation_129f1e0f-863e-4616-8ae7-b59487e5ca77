import time
import json
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from googlemaps import take_screenshot
from agent import StreetViewAgent

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def run_street_view_pipeline(driver, data_list):
    """
    Run the street view pipeline for multiple coordinates.

    Args:
        driver: Selenium webdriver
        data_list: List of dictionaries containing object data
    """

    # Create a copy of the data list to modify
    processed_data = data_list.copy()

    for idx, obj_data in enumerate(data_list):
        print(f"\n{'='*60}")
        print(f"Processing object {idx+1}/{len(data_list)}: ID {obj_data['bbox_id']}")
        print(f"{'='*60}")

        try:
            # Extract latitude and longitude from center_geo
            # Note: center_geo is [longitude, latitude, height]
            lng, lat, _ = obj_data['center_geo']

            # Step 1: Open Street View for the location
            print(f"\nStep 1: Opening Street View for coordinates: {lat}, {lng}...")
            # Use the direct URL approach from googlemaps.py
            street_view_url = f"https://www.google.com/maps/@{lat},{lng},4a,75y,90h,90t/data=!3m1!1e1"
            print(f"Opening Street View URL: {street_view_url}")
            driver.get(street_view_url)

            # Wait longer for Street View to load completely
            print("Waiting for Street View to load completely...")
            time.sleep(8)  # Increased wait time

            # Try to close the side panel if it exists
            try:
                from selenium.webdriver.common.by import By
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                close_panel_button = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, ".yra0jd.Hk4XGb"))
                )
                driver.execute_script("arguments[0].click();", close_panel_button)
                print("Side panel closed successfully")
            except Exception as panel_error:
                print(f"Note: Could not close side panel: {panel_error}")
                # Continue anyway as this is not critical
                pass

            # Step 2: Align the camera with the street and identify object
            print("\nStep 2: Identifying object...")
            street_agent = StreetViewAgent(max_iterations=1)
            obj_name = street_agent.run()

            screenshot_path = take_screenshot(driver, lat, lng, file_name=f"{obj_name}_{lat}_{lng}")

            # Add object_name to the processed data
            processed_data[idx]['object_name'] = obj_name
            print(f"Identified object: {obj_name}")

        except Exception as e:
            print(f"Error processing location {lat}, {lng}: {e}")
            processed_data[idx]['object_name'] = "Error: " + str(e)

        # Wait between locations
        if idx < len(data_list) - 1:
            print("\nWaiting before processing next location...")
            time.sleep(1)

    return processed_data

# Example usage
if __name__ == "__main__":
    # The JSON data directly included in the code
    data_list = [
        {
            "bbox_id": 0,
            "center_geo": [
                14.392851,
                49.985851,
                86.78303162240894
            ]
        }
    ]

    print(f"Processing {len(data_list)} objects")

    # Set up Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")  # Start with max window size

    # Initialize the driver with ChromeDriverManager
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)

    # Run the pipeline
    processed_data = run_street_view_pipeline(
        driver=driver,
        data_list=data_list
    )

    # Save the processed data with object names
    try:
        output_file = f"processed_objects_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(processed_data, f, indent=2)
        print(f"\nProcessed data saved to {output_file}")
    except Exception as e:
        print(f"Error saving processed data: {e}")

    driver.quit()