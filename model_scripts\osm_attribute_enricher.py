#!/usr/bin/env python3
"""
OSM Attribute Enricher
Queries OpenStreetMap data and associates attributes with model-generated line strings
"""

import numpy as np
import pandas as pd
import geopandas as gpd
from pathlib import Path
from shapely.geometry import Polygon, Point
from shapely.ops import transform
import overpy
import time
from pyproj import Transformer
import json

class OSMAttributeEnricher:
    """Enrich model lines with OSM attributes"""
    
    def __init__(self, output_dir):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.osm_data = None
        self.model_lines = None
        
    def load_model_lines(self):
        """Load model-generated line strings"""
        model_lines_file = self.output_dir / "model_lines_only.geojson"
        
        if not model_lines_file.exists():
            raise FileNotFoundError(f"Model lines file not found: {model_lines_file}")
        
        self.model_lines = gpd.read_file(model_lines_file)
        print(f"[INFO] Loaded {len(self.model_lines)} model-generated line strings")
        
        return self.model_lines
    
    def get_study_area_bounds(self):
        """Get bounds of the study area from model lines"""
        if self.model_lines is None:
            raise ValueError("Model lines not loaded")
        
        # Get bounds in current CRS (EPSG:25832)
        bounds = self.model_lines.total_bounds
        
        # Add buffer (500m)
        buffer = 500
        bounds_buffered = [
            bounds[0] - buffer,  # minx
            bounds[1] - buffer,  # miny  
            bounds[2] + buffer,  # maxx
            bounds[3] + buffer   # maxy
        ]
        
        print(f"[INFO] Study area bounds (UTM32N): {bounds_buffered}")
        
        return bounds_buffered
    
    def transform_bounds_to_wgs84(self, bounds_utm):
        """Transform UTM bounds to WGS84 for OSM query"""
        # Create transformer from UTM32N to WGS84
        transformer = Transformer.from_crs("EPSG:25832", "EPSG:4326", always_xy=True)
        
        # Transform corner points
        min_lon, min_lat = transformer.transform(bounds_utm[0], bounds_utm[1])
        max_lon, max_lat = transformer.transform(bounds_utm[2], bounds_utm[3])
        
        wgs84_bounds = [min_lon, min_lat, max_lon, max_lat]
        print(f"[INFO] Study area bounds (WGS84): {wgs84_bounds}")
        
        return wgs84_bounds
    
    def query_osm_data(self, wgs84_bounds):
        """Query OSM for highway/road data in the study area"""
        print("[INFO] Querying OpenStreetMap for highway data...")
        
        # Create Overpass API instance
        api = overpy.Overpass()
        
        # Define bounding box
        south, west, north, east = wgs84_bounds[1], wgs84_bounds[0], wgs84_bounds[3], wgs84_bounds[2]
        
        # Overpass query for highways and roads with complete geometry
        query = f"""
        [out:json][timeout:60];
        (
          way["highway"]({south},{west},{north},{east});
          relation["highway"]({south},{west},{north},{east});
        );
        (._;>;);
        out geom;
        """
        
        try:
            print("[INFO] Executing Overpass query...")
            result = api.query(query)

            print(f"[INFO] Found {len(result.ways)} ways and {len(result.relations)} relations")

            # Process ways
            features = []

            for way in result.ways:
                try:
                    # Extract coordinates - handle missing nodes gracefully
                    coords = []
                    for node in way.nodes:
                        if hasattr(node, 'lon') and hasattr(node, 'lat'):
                            coords.append((float(node.lon), float(node.lat)))

                    if len(coords) < 2:
                        continue

                    # Extract all tags as properties
                    properties = dict(way.tags)
                    properties['osm_id'] = way.id
                    properties['osm_type'] = 'way'

                    features.append({
                        'geometry': {'type': 'LineString', 'coordinates': coords},
                        'properties': properties
                    })

                except Exception as way_error:
                    print(f"[WARNING] Skipping way {way.id}: {str(way_error)}")
                    continue

            print(f"[INFO] Processed {len(features)} OSM features")

            return features

        except Exception as e:
            print(f"[WARNING] OSM query failed: {str(e)}")
            print("[INFO] Continuing without OSM data...")
            return []
    
    def create_osm_geodataframe(self, osm_features):
        """Create GeoDataFrame from OSM features"""
        if not osm_features:
            print("[WARNING] No OSM features to process")
            return gpd.GeoDataFrame()
        
        # Convert to GeoDataFrame
        osm_gdf = gpd.GeoDataFrame.from_features(osm_features, crs="EPSG:4326")
        
        # Transform to UTM32N to match model lines
        osm_gdf = osm_gdf.to_crs("EPSG:25832")
        
        print(f"[INFO] Created OSM GeoDataFrame with {len(osm_gdf)} features")
        
        # Save raw OSM data
        osm_file = self.output_dir / "osm_raw_data.geojson"
        osm_gdf.to_file(osm_file, driver="GeoJSON")
        print(f"[INFO] Saved raw OSM data: {osm_file}")
        
        return osm_gdf
    
    def associate_osm_attributes(self, osm_gdf):
        """Associate OSM attributes with model lines based on proximity"""
        print("[INFO] Associating OSM attributes with model lines...")

        # Create a copy of model lines for enrichment
        enriched_lines = self.model_lines.copy()

        # Always initialize osm_distance column
        enriched_lines['osm_distance'] = None

        if osm_gdf.empty:
            print("[WARNING] No OSM data available for association")
            return enriched_lines

        # Initialize OSM attribute columns
        osm_columns = set()
        for _, osm_feature in osm_gdf.iterrows():
            for key in osm_feature.to_dict().keys():
                if key != 'geometry':
                    osm_columns.add(f"osm_{key}")

        # Initialize columns with None
        for col in osm_columns:
            enriched_lines[col] = None
        
        # For each model line, find the closest OSM feature
        for idx, model_line in enriched_lines.iterrows():
            model_geom = model_line.geometry
            
            # Calculate distances to all OSM features
            distances = osm_gdf.geometry.distance(model_geom)
            
            if len(distances) > 0:
                # Find closest OSM feature
                closest_idx = distances.idxmin()
                closest_distance = distances.iloc[closest_idx]
                
                # Only associate if within reasonable distance (50m)
                if closest_distance <= 50:
                    closest_osm = osm_gdf.iloc[closest_idx]
                    
                    # Copy OSM attributes
                    for key, value in closest_osm.to_dict().items():
                        if key != 'geometry':
                            osm_key = f"osm_{key}"
                            if osm_key in enriched_lines.columns:
                                enriched_lines.at[idx, osm_key] = value
                    
                    # Add distance information
                    enriched_lines.at[idx, 'osm_distance'] = round(closest_distance, 2)

        # Count how many lines got OSM attributes
        lines_with_osm = enriched_lines['osm_distance'].notna().sum()
        print(f"[INFO] Associated OSM attributes with {lines_with_osm}/{len(enriched_lines)} model lines")

        return enriched_lines
    
    def save_enriched_lines(self, enriched_lines):
        """Save enriched lines with OSM attributes"""
        output_file = self.output_dir / "model_lines_with_osm.geojson"
        
        # Clean up None values for JSON serialization
        enriched_clean = enriched_lines.copy()
        for col in enriched_clean.columns:
            if col != 'geometry':
                enriched_clean[col] = enriched_clean[col].fillna('')
        
        enriched_clean.to_file(output_file, driver="GeoJSON")
        
        print(f"[SUCCESS] Saved enriched lines: {output_file}")
        
        # Print statistics
        lines_with_osm = (enriched_lines['osm_distance'].notna()).sum()
        print(f"[INFO] {lines_with_osm}/{len(enriched_lines)} lines have OSM attributes")
        
        if lines_with_osm > 0:
            avg_distance = enriched_lines['osm_distance'].mean()
            print(f"[INFO] Average distance to OSM features: {avg_distance:.2f}m")
        
        return output_file

def main():
    """Main execution function"""
    print("[INFO] Starting OSM Attribute Enrichment")
    print("-" * 50)
    
    # Configuration
    output_dir = Path(__file__).parent
    
    # Initialize enricher
    enricher = OSMAttributeEnricher(output_dir)
    
    try:
        # Load model lines
        model_lines = enricher.load_model_lines()
        
        # Get study area bounds
        utm_bounds = enricher.get_study_area_bounds()
        wgs84_bounds = enricher.transform_bounds_to_wgs84(utm_bounds)
        
        # Query OSM data
        osm_features = enricher.query_osm_data(wgs84_bounds)
        
        # Create OSM GeoDataFrame
        osm_gdf = enricher.create_osm_geodataframe(osm_features)
        
        # Associate OSM attributes with model lines
        enriched_lines = enricher.associate_osm_attributes(osm_gdf)
        
        # Save enriched results
        output_file = enricher.save_enriched_lines(enriched_lines)
        
        print(f"[SUCCESS] OSM attribute enrichment completed!")
        
    except Exception as e:
        print(f"[ERROR] OSM attribute enrichment failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
