#!/usr/bin/env python3
"""
Efficient Chunked LAS to Lane Centerlines Extractor
Processes large LAS files in spatial chunks to avoid memory issues and line breakages
Maintains OSM integration capabilities from the original version
"""

import argparse
import laspy
import pandas as pd
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from sklearn.linear_model import RANSACRegressor, LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
from shapely.geometry import MultiPoint, LineString, Point
from shapely.ops import unary_union, linemerge
import geopandas as gpd
import matplotlib.pyplot as plt
import sys
from pathlib import Path
import shutil
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class ChunkedLaneExtractor:
    """Efficient chunked processing for lane extraction from LAS files"""
    
    def __init__(self, las_file, chunk_size=100, overlap=20):
        self.las_file = Path(las_file)
        self.chunk_size = chunk_size  # meters
        self.overlap = overlap  # meters overlap between chunks
        self.all_lines = []
        self.chunk_lines = defaultdict(list)
        self.global_cluster_id = 0
        
    def load_las_header_info(self):
        """Load LAS header information without loading all points"""
        print(f"[INFO] Reading LAS header: {self.las_file}")
        
        with laspy.open(self.las_file) as las_file:
            header = las_file.header
            
            # Get bounds
            bounds = {
                'min_x': header.min[0],
                'max_x': header.max[0], 
                'min_y': header.min[1],
                'max_y': header.max[1],
                'min_z': header.min[2],
                'max_z': header.max[2]
            }
            
            # Get CRS
            crs = header.parse_crs()
            if crs is None:
                raise RuntimeError("LAS header has no CRS—please specify EPSG code")
            
            point_count = header.point_count
            
        print(f"[INFO] LAS bounds: X({bounds['min_x']:.2f}, {bounds['max_x']:.2f}), "
              f"Y({bounds['min_y']:.2f}, {bounds['max_y']:.2f})")
        print(f"[INFO] Total points: {point_count:,}")
        
        return bounds, crs, point_count
    
    def create_chunk_grid(self, bounds):
        """Create overlapping spatial chunks"""
        min_x, max_x = bounds['min_x'], bounds['max_x']
        min_y, max_y = bounds['min_y'], bounds['max_y']
        
        # Calculate chunk grid
        x_range = max_x - min_x
        y_range = max_y - min_y
        
        # Number of chunks (without overlap consideration)
        x_chunks = int(np.ceil(x_range / self.chunk_size))
        y_chunks = int(np.ceil(y_range / self.chunk_size))
        
        print(f"[INFO] Creating {x_chunks} x {y_chunks} = {x_chunks * y_chunks} overlapping chunks")
        print(f"[INFO] Chunk size: {self.chunk_size}m, Overlap: {self.overlap}m")
        
        chunks = []
        chunk_id = 0
        
        for i in range(x_chunks):
            for j in range(y_chunks):
                # Calculate chunk bounds with overlap
                x_min = min_x + i * self.chunk_size - self.overlap
                x_max = min_x + (i + 1) * self.chunk_size + self.overlap
                y_min = min_y + j * self.chunk_size - self.overlap
                y_max = min_y + (j + 1) * self.chunk_size + self.overlap
                
                # Clip to data bounds
                x_min = max(x_min, min_x)
                x_max = min(x_max, max_x)
                y_min = max(y_min, min_y)
                y_max = min(y_max, max_y)
                
                chunks.append({
                    'id': chunk_id,
                    'grid_i': i,
                    'grid_j': j,
                    'bounds': (x_min, y_min, x_max, y_max),
                    'core_bounds': (
                        min_x + i * self.chunk_size,
                        min_y + j * self.chunk_size,
                        min_x + (i + 1) * self.chunk_size,
                        min_y + (j + 1) * self.chunk_size
                    )
                })
                chunk_id += 1
        
        return chunks
    
    def load_chunk_data(self, chunk_bounds, intensity_thresh=30.0):
        """Load points for a specific chunk"""
        x_min, y_min, x_max, y_max = chunk_bounds
        
        # Read LAS file and filter spatially
        las = laspy.read(self.las_file)
        xyz = las.xyz
        intensity = np.array(las.intensity, dtype=float)
        
        # Spatial filtering
        spatial_mask = (
            (xyz[:, 0] >= x_min) & (xyz[:, 0] <= x_max) &
            (xyz[:, 1] >= y_min) & (xyz[:, 1] <= y_max)
        )
        
        # Intensity filtering
        intensity_mask = intensity >= intensity_thresh
        
        # Combined mask
        combined_mask = spatial_mask & intensity_mask
        
        if not np.any(combined_mask):
            return None
        
        # Extract filtered data
        chunk_xyz = xyz[combined_mask]
        chunk_intensity = intensity[combined_mask]
        
        return {
            'xyz': chunk_xyz,
            'intensity': chunk_intensity,
            'point_count': len(chunk_xyz)
        }
    
    def apply_ransac_ground_filtering(self, points, ground_threshold=0.05):
        """Apply RANSAC ground plane filtering to chunk points"""
        if len(points) < 10:
            return points
        
        try:
            # Prepare data for RANSAC
            XY = points[:, :2]  # X, Y coordinates
            Z = points[:, 2]    # Z coordinates
            
            # Create RANSAC model for ground plane fitting
            plane_model = make_pipeline(
                PolynomialFeatures(degree=1, include_bias=True),
                RANSACRegressor(
                    estimator=LinearRegression(),
                    residual_threshold=ground_threshold,
                    random_state=0,
                    max_trials=100
                )
            )
            
            # Fit the model
            plane_model.fit(XY, Z)
            Z_pred = plane_model.predict(XY)
            
            # Filter ground points
            residuals = np.abs(Z - Z_pred)
            ground_mask = residuals <= ground_threshold
            
            return points[ground_mask]
            
        except Exception as e:
            print(f"[WARNING] RANSAC filtering failed: {e}")
            return points
    
    def extract_lines_from_chunk(self, chunk_data, chunk_info, eps=0.5, min_samples=10):
        """Extract centerlines from a single chunk using improved hull-slice method"""
        if chunk_data is None or chunk_data['point_count'] < min_samples:
            return []
        
        points = chunk_data['xyz']
        chunk_id = chunk_info['id']
        core_bounds = chunk_info['core_bounds']
        
        # Apply RANSAC ground filtering
        ground_points = self.apply_ransac_ground_filtering(points)
        
        if len(ground_points) < min_samples:
            return []
        
        # Use 2D coordinates for clustering
        coords_2d = ground_points[:, :2]
        
        # DBSCAN clustering
        db = DBSCAN(eps=eps, min_samples=min_samples).fit(coords_2d)
        labels = db.labels_
        
        unique_labels = set(labels)
        if -1 in unique_labels:
            unique_labels.remove(-1)  # Remove noise
        
        chunk_lines = []
        
        for label in unique_labels:
            cluster_mask = labels == label
            cluster_points = coords_2d[cluster_mask]
            
            if len(cluster_points) < min_samples:
                continue
            
            try:
                # Create convex hull
                hull = MultiPoint(cluster_points).convex_hull
                
                # PCA for principal direction
                pca = PCA(n_components=2).fit(cluster_points)
                center = pca.mean_
                direction = pca.components_[0]
                explained_variance_ratio = pca.explained_variance_ratio_
                
                # Check linearity
                if explained_variance_ratio[0] < 0.8:
                    continue
                
                # Create extended line for hull intersection
                span = max(
                    hull.bounds[2] - hull.bounds[0],
                    hull.bounds[3] - hull.bounds[1]
                ) * 2.0
                
                extended_line = LineString([
                    tuple(center - direction * span),
                    tuple(center + direction * span)
                ])
                
                # Hull-slice intersection
                chord = hull.intersection(extended_line)
                
                # Handle different geometry types
                if hasattr(chord, 'geom_type'):
                    if chord.geom_type == 'MultiLineString':
                        chord = max(chord.geoms, key=lambda seg: seg.length)
                    elif chord.geom_type != 'LineString':
                        continue
                else:
                    continue
                
                # Check if line center is in core bounds (avoid edge effects)
                line_center = Point(chord.centroid.x, chord.centroid.y)
                core_x_min, core_y_min, core_x_max, core_y_max = core_bounds
                
                in_core = (core_x_min <= line_center.x <= core_x_max and 
                          core_y_min <= line_center.y <= core_y_max)
                
                # Store line with metadata
                line_data = {
                    'geometry': chord,
                    'chunk_id': chunk_id,
                    'cluster_id': self.global_cluster_id,
                    'local_cluster_id': int(label),
                    'point_count': len(cluster_points),
                    'length': chord.length,
                    'explained_variance': explained_variance_ratio[0],
                    'in_core': in_core,
                    'grid_i': chunk_info['grid_i'],
                    'grid_j': chunk_info['grid_j']
                }
                
                chunk_lines.append(line_data)
                self.global_cluster_id += 1
                
            except Exception as e:
                print(f"[WARNING] Failed to process cluster {label} in chunk {chunk_id}: {e}")
                continue
        
        return chunk_lines
    
    def process_all_chunks(self, chunks, intensity_thresh=20.0, eps=0.5, min_samples=10):
        """Process all chunks and extract lines"""
        print(f"[INFO] Processing {len(chunks)} chunks...")
        
        total_lines = 0
        processed_chunks = 0
        
        for i, chunk in enumerate(chunks):
            if i % 10 == 0:
                print(f"[INFO] Processing chunk {i+1}/{len(chunks)}")
            
            # Load chunk data
            chunk_data = self.load_chunk_data(chunk['bounds'], intensity_thresh)
            
            if chunk_data is None:
                continue
            
            # Extract lines from chunk
            chunk_lines = self.extract_lines_from_chunk(
                chunk_data, chunk, eps=eps, min_samples=min_samples
            )
            
            # Store lines
            self.chunk_lines[chunk['id']] = chunk_lines
            self.all_lines.extend(chunk_lines)
            
            total_lines += len(chunk_lines)
            processed_chunks += 1
        
        print(f"[INFO] Processed {processed_chunks} chunks, extracted {total_lines} line segments")
        return total_lines

    def merge_overlapping_lines(self, distance_threshold=2.0):
        """Merge overlapping lines from adjacent chunks to create continuous lines"""
        print(f"[INFO] Merging overlapping lines (threshold: {distance_threshold}m)")

        if len(self.all_lines) == 0:
            return []

        # Group lines by spatial proximity
        merged_lines = []
        used_indices = set()

        for i, line1 in enumerate(self.all_lines):
            if i in used_indices:
                continue

            # Start a new merged line group
            line_group = [line1]
            used_indices.add(i)

            # Find nearby lines to merge
            for j, line2 in enumerate(self.all_lines):
                if j in used_indices or i == j:
                    continue

                # Check if lines are close enough to merge
                geom1 = line1['geometry']
                geom2 = line2['geometry']

                # Check distance between line endpoints
                min_distance = min(
                    Point(geom1.coords[0]).distance(Point(geom2.coords[0])),
                    Point(geom1.coords[0]).distance(Point(geom2.coords[-1])),
                    Point(geom1.coords[-1]).distance(Point(geom2.coords[0])),
                    Point(geom1.coords[-1]).distance(Point(geom2.coords[-1]))
                )

                if min_distance <= distance_threshold:
                    line_group.append(line2)
                    used_indices.add(j)

            # Merge the line group
            if len(line_group) == 1:
                merged_lines.append(line_group[0])
            else:
                merged_line = self.merge_line_group(line_group)
                if merged_line:
                    merged_lines.append(merged_line)

        print(f"[INFO] Merged {len(self.all_lines)} segments into {len(merged_lines)} continuous lines")
        return merged_lines

    def merge_line_group(self, line_group):
        """Merge a group of nearby lines into a single continuous line"""
        try:
            # Extract geometries
            geometries = [line['geometry'] for line in line_group]

            # Try to merge using linemerge
            merged_geom = linemerge(geometries)

            # If linemerge returns MultiLineString, take the longest segment
            if hasattr(merged_geom, 'geom_type'):
                if merged_geom.geom_type == 'MultiLineString':
                    merged_geom = max(merged_geom.geoms, key=lambda g: g.length)
                elif merged_geom.geom_type != 'LineString':
                    # Fallback: use the longest original line
                    merged_geom = max(geometries, key=lambda g: g.length)

            # Combine properties
            total_points = sum(line['point_count'] for line in line_group)
            avg_variance = np.mean([line['explained_variance'] for line in line_group])
            chunk_ids = [line['chunk_id'] for line in line_group]

            merged_line = {
                'geometry': merged_geom,
                'chunk_id': f"merged_{min(chunk_ids)}_{max(chunk_ids)}",
                'cluster_id': line_group[0]['cluster_id'],  # Use first cluster ID
                'point_count': total_points,
                'length': merged_geom.length,
                'explained_variance': avg_variance,
                'merged_from': len(line_group),
                'source_chunks': chunk_ids
            }

            return merged_line

        except Exception as e:
            print(f"[WARNING] Failed to merge line group: {e}")
            # Return the longest line from the group as fallback
            return max(line_group, key=lambda line: line['geometry'].length)

    def filter_short_lines(self, lines, min_length=5.0):
        """Filter out very short lines that are likely noise"""
        filtered_lines = [line for line in lines if line['geometry'].length >= min_length]
        removed_count = len(lines) - len(filtered_lines)

        if removed_count > 0:
            print(f"[INFO] Filtered out {removed_count} short lines (< {min_length}m)")

        return filtered_lines

    def create_geodataframes(self, lines, crs):
        """Create GeoDataFrames from extracted lines"""
        if not lines:
            return gpd.GeoDataFrame(geometry=[], crs=crs)

        # Prepare data for GeoDataFrame
        geometries = []
        properties = []

        for i, line in enumerate(lines):
            geometries.append(line['geometry'])

            # Create properties dictionary
            props = {
                'line_id': i,
                'cluster_id': line.get('cluster_id', i),
                'point_count': line.get('point_count', 0),
                'length': round(line['geometry'].length, 2),
                'explained_variance': round(line.get('explained_variance', 0), 3),
                'source': line.get('chunk_id', 'unknown')
            }

            # Add merge information if available
            if 'merged_from' in line:
                props['merged_from'] = line['merged_from']
                props['source_chunks'] = str(line.get('source_chunks', []))

            properties.append(props)

        # Create GeoDataFrame
        gdf = gpd.GeoDataFrame(properties, geometry=geometries, crs=crs)
        return gdf


def integrate_osm_attributes(gdf_lines, output_prefix, output_dir=None):
    """Enhanced OSM integration using the local OSM enricher with improved effectiveness"""
    try:
        print("[INFO] Starting enhanced OSM integration...")

        # Use provided output directory or create temporary one
        if output_dir is None:
            temp_dir = Path("temp_osm_enrichment")
            temp_dir.mkdir(exist_ok=True)
            cleanup_temp = True
        else:
            temp_dir = Path(output_dir)
            temp_dir.mkdir(exist_ok=True)
            cleanup_temp = False

        # Save lines as model_lines_only.geojson for the enricher
        temp_model_file = temp_dir / "model_lines_only.geojson"
        gdf_lines.to_file(temp_model_file, driver="GeoJSON")
        print(f"[INFO] Saved model lines for OSM processing: {temp_model_file}")

        # Import the local OSM enricher
        current_dir = Path(__file__).parent
        osm_enricher_path = current_dir / "osm_attribute_enricher.py"

        if not osm_enricher_path.exists():
            print(f"[WARNING] OSM enricher not found at: {osm_enricher_path}")
            print(f"[INFO] Skipping OSM integration")
            return gdf_lines, gpd.GeoDataFrame(geometry=[], crs=gdf_lines.crs)

        # Import the OSM enricher class
        sys.path.insert(0, str(current_dir))

        try:
            from osm_attribute_enricher import OSMAttributeEnricher

            # Create enricher instance
            enricher = OSMAttributeEnricher(temp_dir)

            # Load the model lines
            enricher.load_model_lines()

            # Get study area bounds and query OSM
            utm_bounds = enricher.get_study_area_bounds()
            wgs84_bounds = enricher.transform_bounds_to_wgs84(utm_bounds)

            print(f"[INFO] Querying OSM for bounds: {wgs84_bounds}")
            print(f"[INFO] Study area: {utm_bounds[2]-utm_bounds[0]:.0f}m × {utm_bounds[3]-utm_bounds[1]:.0f}m")

            # Query OSM data with enhanced error handling
            osm_features = enricher.query_osm_data(wgs84_bounds)

            if len(osm_features) > 0:
                print(f"[INFO] Retrieved {len(osm_features)} OSM features")

                # Convert to GeoDataFrame
                osm_gdf = enricher.create_osm_geodataframe(osm_features)

                if len(osm_gdf) > 0:
                    # Save raw OSM data with enhanced naming
                    osm_output_file = f"{output_prefix}_osm_data.geojson"
                    osm_gdf.to_file(osm_output_file, driver="GeoJSON")
                    print(f"[INFO] Saved OSM data: {osm_output_file}")

                    # Print OSM data statistics
                    print_osm_statistics(osm_gdf)

                    # Associate OSM attributes with lines using enhanced method
                    enriched_lines = enricher.associate_osm_attributes(osm_gdf)

                    # Save enriched lines with better naming
                    enriched_output_file = f"{output_prefix}_enriched_centerlines.geojson"
                    enriched_lines.to_file(enriched_output_file, driver="GeoJSON")
                    print(f"[INFO] Saved enriched centerlines: {enriched_output_file}")

                    # Print enrichment statistics
                    print_enrichment_statistics(enriched_lines)

                    print(f"[SUCCESS] OSM enrichment completed successfully")

                    # Clean up temp directory if we created it
                    if cleanup_temp:
                        shutil.rmtree(temp_dir, ignore_errors=True)

                    return enriched_lines, osm_gdf
                else:
                    print("[WARNING] No OSM GeoDataFrame created")
            else:
                print("[WARNING] No OSM features found in the area")
                print("[INFO] This could be due to:")
                print("  - Remote area with no mapped roads")
                print("  - Network connectivity issues")
                print("  - Overpass API timeout")

        except ImportError as e:
            print(f"[WARNING] Could not import OSM enricher: {e}")
        except Exception as e:
            print(f"[WARNING] OSM enrichment failed: {e}")
            import traceback
            traceback.print_exc()

        # Clean up temp directory if we created it
        if cleanup_temp:
            shutil.rmtree(temp_dir, ignore_errors=True)

        return gdf_lines, gpd.GeoDataFrame(geometry=[], crs=gdf_lines.crs)

    except Exception as e:
        print(f"[ERROR] OSM integration failed: {e}")
        import traceback
        traceback.print_exc()
        return gdf_lines, gpd.GeoDataFrame(geometry=[], crs=gdf_lines.crs)


def print_osm_statistics(osm_gdf):
    """Print detailed OSM data statistics"""
    print("\n" + "="*50)
    print("OSM DATA STATISTICS")
    print("="*50)

    # Highway types
    if 'highway' in osm_gdf.columns:
        highway_counts = osm_gdf['highway'].value_counts()
        print(f"Highway types found:")
        for highway_type, count in highway_counts.head(10).items():
            print(f"  {highway_type}: {count}")

    # Other attributes
    attribute_counts = {}
    for col in osm_gdf.columns:
        if col not in ['geometry', 'osm_id', 'osm_type']:
            non_null_count = osm_gdf[col].notna().sum()
            if non_null_count > 0:
                attribute_counts[col] = non_null_count

    if attribute_counts:
        print(f"\nOSM attributes available:")
        for attr, count in sorted(attribute_counts.items(), key=lambda x: x[1], reverse=True)[:15]:
            print(f"  {attr}: {count} features")


def print_enrichment_statistics(enriched_lines):
    """Print detailed enrichment statistics"""
    print("\n" + "="*50)
    print("ENRICHMENT STATISTICS")
    print("="*50)

    total_lines = len(enriched_lines)
    enriched_count = enriched_lines['osm_distance'].notna().sum()

    print(f"Total centerlines: {total_lines}")
    print(f"OSM-enriched lines: {enriched_count}")
    print(f"Enrichment rate: {enriched_count/total_lines*100:.1f}%")

    if enriched_count > 0:
        # Distance statistics
        distances = enriched_lines['osm_distance'].dropna()
        print(f"Distance to OSM features:")
        print(f"  Mean: {distances.mean():.1f}m")
        print(f"  Median: {distances.median():.1f}m")
        print(f"  Max: {distances.max():.1f}m")

        # Highway type distribution
        enriched_subset = enriched_lines[enriched_lines['osm_distance'].notna()]
        if 'osm_highway' in enriched_subset.columns:
            highway_types = enriched_subset['osm_highway'].value_counts()
            print(f"\nHighway types in enriched lines:")
            for highway_type, count in highway_types.head(8).items():
                print(f"  {highway_type}: {count}")

        # Speed limits
        if 'osm_maxspeed' in enriched_subset.columns:
            speeds = enriched_subset['osm_maxspeed'].value_counts()
            if len(speeds) > 0:
                print(f"\nSpeed limits found:")
                for speed, count in speeds.head(5).items():
                    print(f"  {speed}: {count}")

        # Lane information
        if 'osm_lanes' in enriched_subset.columns:
            lanes = enriched_subset['osm_lanes'].value_counts()
            if len(lanes) > 0:
                print(f"\nLane counts found:")
                for lane_count, count in lanes.head(5).items():
                    print(f"  {lane_count} lanes: {count}")

    print("="*50)


def create_debug_plots(merged_lines, output_prefix):
    """Create debug visualization plots"""
    try:
        print("[INFO] Creating debug plots...")

        # Plot 1: Chunk grid and extracted lines
        fig1, ax1 = plt.subplots(figsize=(12, 10))

        # Plot all extracted lines
        for line_data in merged_lines:
            geom = line_data['geometry']
            xs, ys = geom.xy
            ax1.plot(xs, ys, 'b-', linewidth=2, alpha=0.7)

        ax1.set_title(f"Extracted Centerlines ({len(merged_lines)} lines)")
        ax1.set_xlabel("X (m)")
        ax1.set_ylabel("Y (m)")
        ax1.axis('equal')
        ax1.grid(True, alpha=0.3)

        fig1.tight_layout()
        fig1.savefig(f"{output_prefix}_chunked_centerlines.png", dpi=300, bbox_inches='tight')
        plt.close(fig1)

        # Plot 2: Line length distribution
        if merged_lines:
            lengths = [line['geometry'].length for line in merged_lines]

            fig2, ax2 = plt.subplots(figsize=(10, 6))
            ax2.hist(lengths, bins=30, alpha=0.7, edgecolor='black')
            ax2.set_title("Line Length Distribution")
            ax2.set_xlabel("Length (m)")
            ax2.set_ylabel("Count")
            ax2.grid(True, alpha=0.3)

            # Add statistics
            mean_length = np.mean(lengths)
            median_length = np.median(lengths)
            ax2.axvline(mean_length, color='red', linestyle='--', label=f'Mean: {mean_length:.1f}m')
            ax2.axvline(median_length, color='orange', linestyle='--', label=f'Median: {median_length:.1f}m')
            ax2.legend()

            fig2.tight_layout()
            fig2.savefig(f"{output_prefix}_length_distribution.png", dpi=300, bbox_inches='tight')
            plt.close(fig2)

        print(f"[INFO] Debug plots saved: {output_prefix}_chunked_centerlines.png, {output_prefix}_length_distribution.png")

    except Exception as e:
        print(f"[WARNING] Failed to create debug plots: {e}")


def main():
    """Main function for chunked lane extraction"""
    parser = argparse.ArgumentParser(
        description="Efficient Chunked LAS → Lane Centerlines with OSM Integration"
    )

    # Input/Output arguments
    parser.add_argument('-i', '--input', required=True,
                       help="Input LAS file path")
    parser.add_argument('-o', '--output-prefix', default='chunked_lanes',
                       help="Output file prefix (default: chunked_lanes)")

    # Processing parameters
    parser.add_argument('--chunk-size', type=float, default=100.0,
                       help="Chunk size in meters (default: 100)")
    parser.add_argument('--overlap', type=float, default=20.0,
                       help="Overlap between chunks in meters (default: 20)")
    parser.add_argument('--intensity-thresh', type=float, default=30.0,
                       help="Minimum intensity threshold (default: 30)")
    parser.add_argument('--ground-threshold', type=float, default=0.05,
                       help="RANSAC ground filtering threshold in meters (default: 0.05)")

    # Clustering parameters
    parser.add_argument('--eps', type=float, default=0.5,
                       help="DBSCAN eps parameter in meters (default: 0.5)")
    parser.add_argument('--min-samples', type=int, default=10,
                       help="DBSCAN min_samples parameter (default: 10)")
    parser.add_argument('--min-length', type=float, default=5.0,
                       help="Minimum line length to keep in meters (default: 5.0)")
    parser.add_argument('--merge-threshold', type=float, default=2.0,
                       help="Distance threshold for merging overlapping lines (default: 2.0)")

    # Output options
    parser.add_argument('--target-crs', default="EPSG:25832",
                       help="Target CRS for output (default: EPSG:25832)")
    parser.add_argument('--integrate-osm', action='store_true',
                       help="Enable OSM data integration")
    parser.add_argument('--create-wgs84', action='store_true',
                       help="Create WGS84 versions for web mapping")
    parser.add_argument('--create-plots', action='store_true',
                       help="Create debug visualization plots")

    args = parser.parse_args()

    try:
        print("="*60)
        print("EFFICIENT CHUNKED LANE EXTRACTION")
        print("="*60)

        # Initialize extractor
        extractor = ChunkedLaneExtractor(
            las_file=args.input,
            chunk_size=args.chunk_size,
            overlap=args.overlap
        )

        # Load LAS header information
        bounds, crs, point_count = extractor.load_las_header_info()

        # Create chunk grid
        chunks = extractor.create_chunk_grid(bounds)

        # Process all chunks
        total_lines = extractor.process_all_chunks(
            chunks=chunks,
            intensity_thresh=args.intensity_thresh,
            eps=args.eps,
            min_samples=args.min_samples
        )

        if total_lines == 0:
            print("[WARNING] No lines extracted from any chunks")
            return

        # Merge overlapping lines from adjacent chunks
        merged_lines = extractor.merge_overlapping_lines(
            distance_threshold=args.merge_threshold
        )

        # Filter short lines
        filtered_lines = extractor.filter_short_lines(
            merged_lines, min_length=args.min_length
        )

        if len(filtered_lines) == 0:
            print("[WARNING] No lines remaining after filtering")
            return

        # Create GeoDataFrames
        print(f"[INFO] Creating GeoDataFrames with {len(filtered_lines)} lines...")

        # Convert to target CRS
        src_crs = crs.to_epsg() or crs.to_string()
        gdf_lines = extractor.create_geodataframes(filtered_lines, src_crs)

        if args.target_crs != src_crs:
            gdf_lines = gdf_lines.to_crs(args.target_crs)

        # OSM Integration
        osm_gdf = gpd.GeoDataFrame(geometry=[], crs=args.target_crs)
        if args.integrate_osm:
            print("[INFO] Starting enhanced OSM integration...")
            gdf_lines, osm_gdf = integrate_osm_attributes(gdf_lines, args.output_prefix)

        # Save outputs
        print("[INFO] Saving output files...")

        # Main centerlines file
        centerlines_file = f"{args.output_prefix}_centerlines.geojson"
        gdf_lines.to_file(centerlines_file, driver="GeoJSON")
        print(f"[INFO] Saved centerlines: {centerlines_file}")

        # OSM enriched lines are already saved by the integrate_osm_attributes function
        # Check if enrichment was successful
        enriched_count = 0
        if args.integrate_osm and 'osm_distance' in gdf_lines.columns:
            enriched_count = gdf_lines['osm_distance'].notna().sum()
            if enriched_count > 0:
                print(f"[INFO] {enriched_count}/{len(gdf_lines)} lines were enriched with OSM data")

        # WGS84 versions
        if args.create_wgs84:
            print("[INFO] Creating WGS84 versions...")

            files_to_convert = [
                (gdf_lines, f"{args.output_prefix}_centerlines_wgs84.geojson")
            ]

            # Add OSM data if available
            if args.integrate_osm and len(osm_gdf) > 0:
                files_to_convert.append(
                    (osm_gdf, f"{args.output_prefix}_osm_data_wgs84.geojson")
                )

            # Add enriched lines if they exist and have OSM data
            if args.integrate_osm and 'osm_distance' in gdf_lines.columns:
                enriched_gdf = gdf_lines[gdf_lines['osm_distance'].notna()].copy()
                if len(enriched_gdf) > 0:
                    files_to_convert.append(
                        (enriched_gdf, f"{args.output_prefix}_enriched_centerlines_wgs84.geojson")
                    )

            for gdf, filename in files_to_convert:
                try:
                    gdf_wgs84 = gdf.to_crs("EPSG:4326")
                    gdf_wgs84.to_file(filename, driver="GeoJSON")
                    print(f"[INFO] Saved WGS84 version: {filename}")
                except Exception as e:
                    print(f"[WARNING] Failed to create WGS84 version of {filename}: {e}")

        # Debug plots
        if args.create_plots:
            create_debug_plots(filtered_lines, args.output_prefix)

        # Print summary
        print("\n" + "="*60)
        print("EXTRACTION SUMMARY")
        print("="*60)
        print(f"Input file: {args.input}")
        print(f"Total input points: {point_count:,}")
        print(f"Chunks processed: {len(chunks)}")
        print(f"Raw line segments: {total_lines}")
        print(f"Merged lines: {len(merged_lines)}")
        print(f"Final centerlines: {len(filtered_lines)}")

        # OSM enrichment statistics
        if args.integrate_osm:
            if 'osm_distance' in gdf_lines.columns:
                enriched_count = gdf_lines['osm_distance'].notna().sum()
                print(f"OSM-enriched lines: {enriched_count}/{len(gdf_lines)} ({enriched_count/len(gdf_lines)*100:.1f}%)")

                if enriched_count > 0:
                    # Show highway types
                    if 'osm_highway' in gdf_lines.columns:
                        highway_types = gdf_lines[gdf_lines['osm_distance'].notna()]['osm_highway'].value_counts()
                        top_highways = dict(highway_types.head(3))
                        print(f"Top highway types: {top_highways}")

                    # Show average distance to OSM features
                    avg_distance = gdf_lines['osm_distance'].mean()
                    print(f"Avg distance to OSM: {avg_distance:.1f}m")
            else:
                print("OSM integration: No enrichment data found")

        # Line statistics
        if filtered_lines:
            lengths = [line['geometry'].length for line in filtered_lines]
            print(f"Line length stats: min={min(lengths):.1f}m, "
                  f"max={max(lengths):.1f}m, mean={np.mean(lengths):.1f}m")

        print(f"\n[SUCCESS] Chunked lane extraction completed!")
        print(f"[INFO] Output files saved with prefix: {args.output_prefix}")

    except Exception as e:
        print(f"[ERROR] Chunked lane extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
