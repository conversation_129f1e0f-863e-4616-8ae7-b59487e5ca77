import time
import json
from typing import Dict, Any, List, Optional
import pyautogui
from PIL import ImageGrab, ImageDraw, ImageFont
from openai import OpenAI
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Initialize OpenAI client
try:
    client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
except Exception as e:
    print(f"Warning: Could not initialize OpenAI client: {e}")
    print("You need to set the OPENAI_API_KEY environment variable.")
    client = None

class StreetViewAgent:
    def __init__(self, max_iterations: int = 1):
        """
        Initialize the Street View Agent.

        Args:
            max_iterations: Maximum number of iterations before stopping
        """
        # Use the API key from environment variables
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set. Make sure to load the .env file.")

        self.client = OpenAI(api_key=api_key)
        self.max_iterations = max_iterations
        self.conversation_history = []

    def take_screenshot(self) -> str:
        """Take a screenshot and return it as a base64 encoded string."""
        # Take screenshot
        screenshot = ImageGrab.grab()

        # Save temporarily and encode to base64
        import io
        import base64

        buffered = io.BytesIO()
        screenshot.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return img_str

    def finish_alignment(self, object_name: str):
        """Return object name identified in the image."""
        print(f"Object identified: {object_name}")
        return object_name

    def execute_tool(self, tool_name: str, **kwargs):
        """Execute the requested tool."""
        if tool_name == "finish_alignment":
            object_name = kwargs.get("object_name", "")
            return self.finish_alignment(object_name)
        else:
            print(f"Unknown tool: {tool_name}")
            return False

    def run(self):
        """Main execution loop for the agent."""
        iteration = 0
        object_identified = None

        # Define available tools for the AI
        tools = [
                    {
                        "type": "function",
                        "function": {
                            "name": "finish_alignment",
                            "description": "Identify the main object at the center of the image using ONLY one of the predefined category names",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "object_name": {
                                        "type": "string",
                                        "description": """Name of the object identified. IMPORTANT: You MUST use EXACTLY ONE of these predefined category names ONLY - no variations allowed:

                - tree (for any individual or group of trees)
                Examples: single oak tree, pine tree, group of palm trees, forest of trees, bare winter trees, flowering trees

                - shrubbery (for bushes, hedges, small plants, grass)
                Examples: garden hedge, low bushes along road, lawn grass, flower beds, small ornamental plants, overgrown shrubs, landscaped greenery

                - road marker (for physical objects on roads like reflectors, cat eyes, bumps only - DO NOT annotate as road marker if it's just painted markings or text on the road)
                Examples: cat's eye reflectors, raised pavement markers, speed bumps, rumble strips, physical road studs, reflective road dots
                NOT included: painted lines, road arrows, written words on asphalt

                - traffic sign (for any road sign)
                Examples: stop sign, yield sign, speed limit sign, no parking sign, warning triangular signs, regulatory square signs, road closure signs, temporary construction signs

                - street light (for lamp posts)
                Examples: traditional street lamp, modern LED light pole, highway lighting, parking lot light fixture, decorative street lighting, traffic light poles without the traffic signals

                - barrier (for any barrier, railing, guardrails, etc)
                Examples: metal guardrails, concrete barriers, safety railings, crash barriers, median barriers, highway dividers, fence-like road barriers

                - direction sign
                Examples: highway exit signs, road direction arrows, lane guidance signs, 'Next Exit' signs, destination signs with arrows

                - road sign
                Examples: informational signs, mile markers, city limit signs, scenic route signs, historical markers on roadside

                - vehicle checkpoint
                Examples: border crossing stations, toll payment booths with gates, security checkpoints, weigh stations, inspection points

                - toll booth
                Examples: highway toll collection booths, toll plaza structures, payment kiosks, EZ-Pass lanes, automated toll stations

                - tunnel
                Examples: highway tunnels, underpasses, road tunnels through mountains. If the image looks like the view from inside a tunnel or the image looks like entry to a tunnel, annotate it as a tunnel.

                - bridge
                Examples: highway overpasses, road bridges over water, viaducts, elevated roadways. if the image is not centered on any object and it looks like the road is heading onto a bridge of sorts, which you can understand by seeing the surroundings and how the elevation of the road changes, annoate it as a bridge. you also be able to tell if the road is surrounded by barriers on both sides along the road,
                or if there is water  on both sides of the bridge or if it looks like we are on a road which is eleveated above the ground level. In these cases, you can annotate it as a bridge.
                remember there can be other objects visble on the image too, but if those objects are not at the center of the image, and it looks like the road is heading onto a bridge or if the view is already from a bridge, then annotate it as a bridge.

                for tunnels and bridges make sure that important or focus point of the image is the tunnel or the bridge, this happens when the image is centered on the road and these cases you will have to draw context from the surroundsing too(it will help you understand if we are trying to understand if the object being pointed out is a bridge or a tunnel).
                """
                                    }
                                },
                                "required": ["object_name"]
                            }
                        }
                    }
                ]

        system_message = """You are an AI agent analyzing images from street view to identify objects at the center of the image.
        Your task is to analyze the current view and determine what specific object is at the center of the image.

        IMPORTANT - GUIDELINES:
        - Identify ONLY the main object at around the CENTER OR MIDDLE of the image
        - Use no more than 3 words to describe the object
        - Focus on objects relevant to autonomous vehicles and driving (traffic signs, road markings, sign boards, barriers, poles street lights, traffic lights, toll booths, vehicle checkpoints,etc.)
        - Be precise and specific in your identification
        - Return ONLY common road/street objects relevant for autonomous driving
        - focus only on the object at around the center of the image
        - sometimes the object might be just above or just below the center of the image, especially if the object is traffic light or overhead sign or road barrier or things like that but they will most definitely still  be ONLY IN THE MIDDLE OF THE IMAGE and not at the edges of the image."

        When you've identified the object at the center of the image, use the finish_alignment tool with the object name."""

        while iteration < self.max_iterations:
            print(f"\n--- Iteration {iteration + 1} ---")

            # Take screenshot
            screenshot_base64 = self.take_screenshot()

            # Add screenshot to conversation
            user_message = {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "What is the main object in this image? Identify it using maximum 3 words."
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{screenshot_base64}"
                        }
                    }
                ]
            }

            self.conversation_history.append(user_message)

            # Get response from AI
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_message},
                    *self.conversation_history
                ],
                tools=tools,
                tool_choice="auto"
            )

            assistant_message = response.choices[0].message
            self.conversation_history.append(assistant_message.model_dump())

            # Print AI's analysis
            if assistant_message.content:
                print(f"AI: {assistant_message.content}")

            # Execute tool if requested
            should_exit = False
            if assistant_message.tool_calls:
                for tool_call in assistant_message.tool_calls:
                    tool_name = tool_call.function.name
                    tool_args = json.loads(tool_call.function.arguments) if tool_call.function.arguments else {}
                    print(f"AI wants to execute: {tool_name} with args: {tool_args}")

                    # Execute the tool and get the object name
                    result = self.execute_tool(tool_name, **tool_args)
                    if tool_name == "finish_alignment":
                        object_identified = result
                        should_exit = True

                    # Add tool execution result to conversation
                    tool_result = {
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": tool_name,
                        "content": f"Successfully executed {tool_name}"
                    }
                    self.conversation_history.append(tool_result)

                    # If we should exit, break out of the tool_calls loop
                    if should_exit:
                        break

                # If we should exit, break out of the main iteration loop
                if should_exit:
                    print("Object identification completed successfully!")
                    break

            iteration += 1

            # Brief pause between iterations
            time.sleep(1)

        if iteration >= self.max_iterations:
            print("\nAgent completed maximum iterations without identifying an object.")

        return object_identified

# Example usage
if __name__ == "__main__":
    # Make sure Google Street View is open and active in your browser
    print("Ensure Google Street View is open and active in your browser...")
    print("Starting in 5 seconds...")
    time.sleep(5)

    # Initialize and run agent
    agent = StreetViewAgent(
        max_iterations=1
    )

    agent.run()