from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time
import os

def open_street_view(driver, lat, lng, wait_time=4):
    """
    Opens Google Street View for given coordinates and closes the side panel.

    Args:
        driver: WebDriver instance
        lat: Latitude
        lng: Longitude
        wait_time: Time to wait for Street View to load (seconds)

    Returns:
        bool: True if Street View was successfully opened, False otherwise
    """
    # Format coordinates
    coords_str = f"{lat},{lng}"

    try:
        # Navigate directly to Google Street View with coordinates
        street_view_url = f"https://www.google.com/maps/@{lat},{lng},4a,75y,90h,90t/data=!3m1!1e1"
        print(f"Opening Street View URL: {street_view_url}")
        driver.get(street_view_url)

        # Wait for the Street View to load
        time.sleep(wait_time)

        # Try to close the side panel if it exists
        try:
            # Wait for the close button to be clickable
            close_panel_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".yra0jd.Hk4XGb"))
            )
            # Use JavaScript to click the button to avoid element intercepted errors
            driver.execute_script("arguments[0].click();", close_panel_button)
            print("Side panel closed successfully")
        except Exception as panel_error:
            print(f"Note: Could not close side panel: {panel_error}")
            # Continue anyway as this is not critical
            pass

        # Additional wait to ensure the view is stable
        time.sleep(2)

        return True

    except Exception as e:
        print(f"Could not access Street View for coordinates {coords_str}: {e}")
        return False

def take_screenshot(driver, lat, lng, file_name=None, output_folder="street_view_images"):
    """
    Take a screenshot of the current browser view and save it to a file.

    Args:
        driver: WebDriver instance
        lat: Latitude (for filename)
        lng: Longitude (for filename)
        file_name: Optional custom filename
        output_folder: Folder to save screenshots

    Returns:
        str: Path to the saved screenshot
    """
    # Create output directory if it doesn't exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"Created directory: {output_folder}")

    # Take screenshot
    if file_name:
        filename = f"{output_folder}/{file_name}.png"
    else:
        # Format coordinates to avoid path issues
        lat_str = str(lat).replace(".", "_")
        lng_str = str(lng).replace(".", "_")
        filename = f"{output_folder}/streetview_{lat_str}_{lng_str}.png"

    # Wait a moment to ensure the view is stable
    time.sleep(1)

    # Take the screenshot
    driver.save_screenshot(filename)
    print(f"Screenshot saved: {filename}")

    return filename

def capture_street_view_screenshots(coordinates_list, output_folder="street_view_images", wait_time=3):
    """
    Capture Google Street View screenshots for a list of coordinates using browser automation.

    Args:
        coordinates_list: List of tuples containing (latitude, longitude) pairs
        output_folder: Folder to save the screenshots
        wait_time: Time to wait for Street View to load (seconds)
    """
    # Set up Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")  # Start with max window size
    # Uncomment the next line if you want to run the browser in headless mode
    # chrome_options.add_argument("--headless")

    # Initialize the driver with ChromeDriverManager
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)

    try:
        print(f"Processing {len(coordinates_list)} locations...")

        for i, (lat, lng) in enumerate(coordinates_list):
            print(f"\n[{i+1}/{len(coordinates_list)}] Processing coordinates: {lat}, {lng}")

            # Open Street View for the coordinates
            success = open_street_view(driver, lat, lng, wait_time)

            if success:
                print(f"Successfully opened Street View for {lat}, {lng}")
            else:
                print(f"Warning: Could not properly open Street View for {lat}, {lng}")

            # Take screenshot regardless of whether Street View opened successfully
            screenshot_path = take_screenshot(driver, lat, lng, output_folder=output_folder)
            print(f"Saved screenshot to {screenshot_path}")

            # Wait between locations
            if i < len(coordinates_list) - 1:
                print("Waiting before processing next location...")
                time.sleep(2)

    except Exception as e:
        print(f"Error during screenshot capture: {e}")

    finally:
        # Close the browser
        print("Closing browser...")
        driver.quit()

# Example usage
if __name__ == "__main__":
    # Example list of coordinates (latitude, longitude)
    coordinates = [
        (52.331193, 13.554153),  # ID 0
        (52.331035, 13.553950),  # ID 6
        (52.331574, 13.554148),  # ID 8
        (52.330545, 13.554192),  # ID 3
        (52.328881, 13.553692),  # ID 2
        # Add the coordinates from the error message
        (49.985851, 14.392851),  # From error message
    ]

    # Create a timestamp for the output folder to keep screenshots organized
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_folder = f"street_view_images_{timestamp}"

    print(f"Starting Street View screenshot capture for {len(coordinates)} locations")
    print(f"Screenshots will be saved to: {output_folder}")

    capture_street_view_screenshots(coordinates, output_folder=output_folder)